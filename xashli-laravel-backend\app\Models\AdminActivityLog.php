<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdminActivityLog extends Model
{
    use HasUuids;

    // Action type constants for consistency and validation
    const ACTION_USER_CREATED = 'user_created';

    const ACTION_USER_UPDATED = 'user_updated';

    const ACTION_USER_DELETED = 'user_deleted';

    const ACTION_DISPUTE_RESOLVED = 'dispute_resolved';

    const ACTION_DISPUTE_REJECTED = 'dispute_rejected';

    const ACTION_PAYMENT_MANUALLY_MATCHED = 'payment_manually_matched';

    const ACTION_AUTO_MATCH_TRIGGERED = 'auto_match_triggered';

    const ACTION_SETTINGS_CREATED = 'settings_created';

    const ACTION_SETTINGS_UPDATED = 'settings_updated';

    const ACTION_SETTINGS_DELETED = 'settings_deleted';

    const ACTION_PLATFORM_FEE_COLLECTED = 'platform_fee_collected';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'admin_id',
        'action_type',
        'target_user_id',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * Get the admin that performed the action.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Get the target user of the action.
     */
    public function targetUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'target_user_id');
    }

    /**
     * Get all valid action types.
     */
    public static function getValidActionTypes(): array
    {
        return [
            self::ACTION_USER_CREATED,
            self::ACTION_USER_UPDATED,
            self::ACTION_USER_DELETED,
            self::ACTION_DISPUTE_RESOLVED,
            self::ACTION_DISPUTE_REJECTED,
            self::ACTION_PAYMENT_MANUALLY_MATCHED,
            self::ACTION_AUTO_MATCH_TRIGGERED,
            self::ACTION_SETTINGS_CREATED,
            self::ACTION_SETTINGS_UPDATED,
            self::ACTION_SETTINGS_DELETED,
            self::ACTION_PLATFORM_FEE_COLLECTED,
        ];
    }

    /**
     * Log an admin activity.
     *
     * @throws \InvalidArgumentException
     */
    public static function log(User $admin, string $actionType, ?User $targetUser = null, array $metadata = []): self
    {
        // Validate action type
        if (! in_array($actionType, self::getValidActionTypes())) {
            throw new \InvalidArgumentException("Invalid action type: {$actionType}");
        }

        return self::create([
            'admin_id' => $admin->id,
            'action_type' => $actionType,
            'target_user_id' => $targetUser?->id,
            'metadata' => $metadata,
        ]);
    }
}
