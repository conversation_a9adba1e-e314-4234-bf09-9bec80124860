import { ArrowLeftRight, CreditCard, Setting<PERSON>, Trophy } from 'lucide-react';
import type { SettingCategory, SettingConfig } from '../types/platformSettings';

export const PLATFORM_SETTINGS_CONFIG: SettingCategory[] = [
  {
    key: 'payment_matching',
    title: 'Payment Matching',
    description: 'Configure automatic payment matching and frequency settings',
    icon: ArrowLeftRight,
    settings: [
      {
        key: 'auto_payment_matching_enabled',
        title: 'Auto Matching',
        description: 'Enable or disable automatic payment matching',
        type: 'boolean',
        validation: { required: true },
      },
      {
        key: 'auto_payment_match_frequency',
        title: 'Matching Frequency',
        description: 'How often to run automatic matching (in hours)',
        type: 'int',
        validation: { min: 1, max: 168, required: true },
        format: 'hours',
        unit: 'hours',
      },
      {
        key: 'confirmation_timeout_hours',
        title: 'Confirmation Timeout',
        description: 'Hours after which unconfirmed matches timeout',
        type: 'int',
        validation: { min: 1, max: 72, required: true },
        format: 'hours',
        unit: 'hours',
      },
    ],
  },
  {
    key: 'fee_management',
    title: 'Fee Management',
    description: 'Configure platform fees, growth rates, and referral bonuses',
    icon: CreditCard,
    settings: [
      {
        key: 'platform_fee_percentage',
        title: 'Platform Fee',
        description: 'Commission percentage taken by the platform (0-10%)',
        type: 'decimal',
        validation: { min: 0, max: 10, step: 0.1, required: true },
        format: 'percentage',
        unit: '%',
      },
      {
        key: 'fiat_growth_rate',
        title: 'Fiat Growth Rate',
        description: 'Growth percentage for fiat currency transactions',
        type: 'decimal',
        validation: { min: 0, max: 100, step: 1, required: true },
        format: 'percentage',
        unit: '%',
      },
      {
        key: 'crypto_growth_rate',
        title: 'Crypto Growth Rate',
        description: 'Growth percentage for cryptocurrency transactions',
        type: 'decimal',
        validation: { min: 0, max: 100, step: 1, required: true },
        format: 'percentage',
        unit: '%',
      },
      {
        key: 'referral_level1_percentage',
        title: 'Level 1 Referral Bonus',
        description: 'Direct referral bonus percentage',
        type: 'decimal',
        validation: { min: 0, max: 20, step: 0.1, required: true },
        format: 'percentage',
        unit: '%',
      },
      {
        key: 'referral_level2_percentage',
        title: 'Level 2 Referral Bonus',
        description: 'Second level referral bonus percentage',
        type: 'decimal',
        validation: { min: 0, max: 10, step: 0.1, required: true },
        format: 'percentage',
        unit: '%',
      },
      {
        key: 'referral_level3_percentage',
        title: 'Level 3 Referral Bonus',
        description: 'Third level referral bonus percentage',
        type: 'decimal',
        validation: { min: 0, max: 5, step: 0.1, required: true },
        format: 'percentage',
        unit: '%',
      },
    ],
  },
  {
    key: 'elite_privileges',
    title: 'Elite Privileges',
    description:
      'Configure elite privilege levels, thresholds, and multipliers',
    icon: Trophy,
    settings: [
      {
        key: 'elite_level_1_priviledge_maximum_fund_multiplier',
        title: 'Elite Level 1 Fund Multiplier',
        description:
          'Maximum fund multiplier for elite level 1 privilege (2x default)',
        type: 'decimal',
        validation: { min: 1, max: 10, step: 0.1, required: true },
        format: 'amount',
        unit: 'x',
      },
      {
        key: 'elite_level_2_priviledge_maximum_fund_multiplier',
        title: 'Elite Level 2 Fund Multiplier',
        description:
          'Maximum fund multiplier for elite level 2 privilege (3x default)',
        type: 'decimal',
        validation: { min: 1, max: 10, step: 0.1, required: true },
        format: 'amount',
        unit: 'x',
      },
      {
        key: 'elite_level_3_priviledge_maximum_fund_multiplier',
        title: 'Elite Level 3 Fund Multiplier',
        description:
          'Maximum fund multiplier for elite level 3 privilege (4x default)',
        type: 'decimal',
        validation: { min: 1, max: 10, step: 0.1, required: true },
        format: 'amount',
        unit: 'x',
      },
      {
        key: 'elite_level_4_priviledge_maximum_fund_multiplier',
        title: 'Elite Level 4 Fund Multiplier',
        description:
          'Maximum fund multiplier for elite level 4 privilege (5x default)',
        type: 'decimal',
        validation: { min: 1, max: 10, step: 0.1, required: true },
        format: 'amount',
        unit: 'x',
      },
      {
        key: 'elite_level_1_transaction_threshold',
        title: 'Elite Level 1 Transaction Threshold',
        description: 'Required successful transactions for elite level 1',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'transactions',
      },
      {
        key: 'elite_level_2_transaction_threshold',
        title: 'Elite Level 2 Transaction Threshold',
        description: 'Required successful transactions for elite level 2',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'transactions',
      },
      {
        key: 'elite_level_3_transaction_threshold',
        title: 'Elite Level 3 Transaction Threshold',
        description: 'Required successful transactions for elite level 3',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'transactions',
      },
      {
        key: 'elite_level_4_transaction_threshold',
        title: 'Elite Level 4 Transaction Threshold',
        description: 'Required successful transactions for elite level 4',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'transactions',
      },
      {
        key: 'elite_level_1_referee_threshold',
        title: 'Elite Level 1 Referee Threshold',
        description: 'Required active referees for elite level 1',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'referees',
      },
      {
        key: 'elite_level_2_referee_threshold',
        title: 'Elite Level 2 Referee Threshold',
        description: 'Required active referees for elite level 2',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'referees',
      },
      {
        key: 'elite_level_3_referee_threshold',
        title: 'Elite Level 3 Referee Threshold',
        description: 'Required active referees for elite level 3',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'referees',
      },
      {
        key: 'elite_level_4_referee_threshold',
        title: 'Elite Level 4 Referee Threshold',
        description: 'Required active referees for elite level 4',
        type: 'int',
        validation: { min: 1, max: 100, required: true },
        format: 'amount',
        unit: 'referees',
      },
    ],
  },
  {
    key: 'system_config',
    title: 'System Configuration',
    description:
      'Configure maturity periods, transaction limits, and system-wide settings',
    icon: Settings,
    settings: [
      {
        key: 'fast_maturity_days',
        title: 'Fast Track Maturity',
        description: 'Days for fast track transactions to mature',
        type: 'int',
        validation: { min: 1, max: 30, required: true },
        format: 'days',
        unit: 'days',
      },
      {
        key: 'standard_maturity_days',
        title: 'Standard Maturity',
        description: 'Days for standard transactions to mature',
        type: 'int',
        validation: { min: 1, max: 60, required: true },
        format: 'days',
        unit: 'days',
      },
      {
        key: 'minimum_fiat_fund_amount',
        title: 'Minimum Fiat Amount',
        description: 'Minimum allowed fiat transaction amount (NGN)',
        type: 'decimal',
        validation: { min: 0, step: 0.01, required: true },
        format: 'currency',
        unit: 'NGN',
      },
      {
        key: 'maximum_fiat_fund_amount',
        title: 'Maximum Fiat Amount',
        description: 'Maximum allowed fiat transaction amount (NGN)',
        type: 'decimal',
        validation: { min: 0, step: 0.01, required: true },
        format: 'currency',
        unit: 'NGN',
      },
      {
        key: 'minimum_crypto_fund_amount',
        title: 'Minimum Crypto Amount',
        description: 'Minimum allowed crypto transaction amount (in SOL)',
        type: 'decimal',
        validation: { min: 0, step: 0.0001, required: true },
        format: 'amount',
        unit: 'SOL',
      },
      {
        key: 'maximum_crypto_fund_amount',
        title: 'Maximum Crypto Amount',
        description: 'Maximum allowed crypto transaction amount (in SOL)',
        type: 'decimal',
        validation: { min: 0, step: 0.0001, required: true },
        format: 'amount',
        unit: 'SOL',
      },
      {
        key: 'fiat_fast_maturity_threshold',
        title: 'Fiat Fast Maturity Threshold',
        description:
          'Minimum total fiat funding from level 1 referees for fast maturity',
        type: 'decimal',
        validation: { min: 0, step: 0.01, required: true },
        format: 'currency',
        unit: 'NGN',
      },
      {
        key: 'crypto_fast_maturity_threshold',
        title: 'Crypto Fast Maturity Threshold',
        description:
          'Minimum total SOL funding from level 1 referees for fast maturity',
        type: 'decimal',
        validation: { min: 0, step: 0.0001, required: true },
        format: 'amount',
        unit: 'SOL',
      },
    ],
  },
];

// Helper function to get setting config by key
export const getSettingConfig = (key: string): SettingConfig | undefined => {
  for (const category of PLATFORM_SETTINGS_CONFIG) {
    const setting = category.settings.find(s => s.key === key);
    if (setting) return setting;
  }
  return undefined;
};

// Helper function to get category for a setting key
export const getCategoryForSetting = (
  key: string
): SettingCategory | undefined => {
  return PLATFORM_SETTINGS_CONFIG.find(category =>
    category.settings.some(s => s.key === key)
  );
};

// Helper function to format setting value for display
export const formatSettingValue = (
  value: string | number | boolean,
  config?: SettingConfig
): string => {
  if (!config) return String(value);

  if (config.type === 'boolean') {
    return value ? 'Enabled' : 'Disabled';
  }

  const numericValue =
    typeof value === 'string' ? parseFloat(value) : Number(value);

  if (isNaN(numericValue)) return String(value);

  switch (config.format) {
    case 'percentage':
      return `${numericValue.toFixed(1)}%`;
    case 'currency':
      return `${config.unit || '$'}${numericValue.toFixed(2)}`;
    case 'hours':
      return `${numericValue} hour${numericValue !== 1 ? 's' : ''}`;
    case 'days':
      return `${numericValue} day${numericValue !== 1 ? 's' : ''}`;
    case 'amount':
      return `${numericValue} ${config.unit || ''}`;
    default:
      return `${value}${config.unit ? ` ${config.unit}` : ''}`;
  }
};

// Helper function to parse display value back to raw value
export const parseSettingValue = (
  displayValue: string,
  config?: SettingConfig
): string => {
  if (!config) return displayValue;

  let cleanValue = displayValue.replace(/[^0-9.-]/g, '');
  const numericValue = parseFloat(cleanValue);

  if (isNaN(numericValue)) return '0';

  switch (config.format) {
    case 'percentage':
      return numericValue.toString();
    default:
      return numericValue.toString();
  }
};

// Validation helper
export const validateSettingValue = (
  value: string | number | boolean,
  config?: SettingConfig
): string | null => {
  if (!config || !config.validation) return null;

  const { validation } = config;

  if (validation.required && (value === '' || value == null)) {
    return 'This field is required';
  }

  if (config.type === 'boolean') return null;

  const numericValue =
    typeof value === 'string' ? parseFloat(value) : Number(value);

  if (isNaN(numericValue)) {
    return 'Please enter a valid number';
  }

  if (validation.min !== undefined && numericValue < validation.min) {
    return `Value must be at least ${validation.min}`;
  }

  if (validation.max !== undefined && numericValue > validation.max) {
    return `Value must be at most ${validation.max}`;
  }

  return null;
};
