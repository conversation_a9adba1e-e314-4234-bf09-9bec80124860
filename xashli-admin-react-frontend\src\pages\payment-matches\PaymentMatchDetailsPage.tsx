import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MainLayout } from '@/components/layout/MainLayout';
import { LoadingSpinner } from '@/components/ui/loading';
import {
  ArrowLeft,
  User,
  CreditCard,
  AlertTriangle,
  Clock,
  Calendar,
  RefreshCw,
  ExternalLink,
  ArrowUp,
  ArrowDown,
  Link,
  Send,
  Upload,
} from 'lucide-react';
import { paymentMatchService } from '@/services/paymentMatch';
import { shortenId, formatCurrencyAmount } from '@/utils/format';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import { AdminPaymentModal } from '@/components/admin-payments/AdminPaymentModal';
import { AdminPaymentProofModal } from '@/components/admin-payments/AdminPaymentProofModal';

const PaymentMatchDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // State for admin payment modals
  const [showAdminPaymentModal, setShowAdminPaymentModal] = useState(false);
  const [showAdminProofModal, setShowAdminProofModal] = useState(false);

  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['payment-match', id],
    queryFn: () => paymentMatchService.getPaymentMatch(id!),
    enabled: !!id,
  });

  const paymentMatch = response?.data;

  // Admin payment mutations
  const confirmPaymentMutation = useMutation({
    mutationFn: ({ transactionHash }: { transactionHash?: string }) => {
      const data: { transaction_hash: string } = {
        transaction_hash: transactionHash || '',
      };
      return paymentMatchService.confirmPaymentSent(id!, data);
    },
    onSuccess: () => {
      toast.success('Payment confirmed successfully');
      setShowAdminPaymentModal(false);
      queryClient.invalidateQueries({ queryKey: ['payment-match', id] });
    },
    onError: error => {
      toast.error('Failed to confirm payment');
      console.error('Payment confirmation error:', error);
    },
  });

  const uploadProofMutation = useMutation({
    mutationFn: (file: File) =>
      paymentMatchService.uploadPaymentProof(id!, file),
    onSuccess: () => {
      toast.success('Payment proof uploaded successfully');
      setShowAdminProofModal(false);
      queryClient.invalidateQueries({ queryKey: ['payment-match', id] });
    },
    onError: error => {
      toast.error('Failed to upload payment proof');
      console.error('Payment proof upload error:', error);
    },
  });

  // Check if current user is admin and this is an admin fund (funder)
  const isAdminFund =
    user?.role === 'admin' && paymentMatch?.is_admin_withdraw === false;
  const canMakePayment =
    isAdminFund &&
    paymentMatch?.status === 'pending' &&
    !paymentMatch?.is_payment_sent_confirmed;
  const canUploadProof =
    isAdminFund &&
    paymentMatch?.is_payment_sent_confirmed &&
    !paymentMatch?.is_payment_received_confirmed;

  const handleRefresh = async () => {
    try {
      await refetch();
      toast.success('Payment match data refreshed');
    } catch (error) {
      toast.error('Failed to refresh data');
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'paid':
        return 'bg-brand-gold-100 text-brand-gold-800 border-brand-gold-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'disputed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-brand-grey-100 text-brand-grey-800 border-brand-grey-200';
    }
  };

  const getInitiatorBadgeColor = (initiator: string) => {
    return initiator === 'manual'
      ? 'bg-purple-100 text-purple-800 border-purple-200'
      : 'bg-brand-grey-100 text-brand-grey-800 border-brand-grey-200';
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </MainLayout>
    );
  }

  if (error || !paymentMatch) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-brand-black mb-2">
              Payment Match Not Found
            </h2>
            <p className="text-brand-grey-600 mb-4">
              The payment match you're looking for doesn't exist or you don't
              have permission to view it.
            </p>
            <Button onClick={() => navigate('/admin/payment-matches')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Payment Matches
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigate('/admin/payment-matches')}
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-brand-black">
                Payment Match Details
              </h1>
              <p className="text-brand-grey-600 mt-1">
                Match ID: #{paymentMatch.id}
              </p>
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-3">
            <Badge className={getStatusBadgeColor(paymentMatch.status)}>
              {paymentMatch.status}
            </Badge>
            <Badge className={getInitiatorBadgeColor(paymentMatch.initiator)}>
              {paymentMatch.initiator}
            </Badge>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Link className="w-5 h-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      Match ID
                    </label>
                    <p className="font-mono text-lg">
                      #{shortenId(paymentMatch.id)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      Amount
                    </label>
                    <p className="text-xl font-semibold">
                      {formatCurrencyAmount(
                        paymentMatch.amount,
                        (paymentMatch.fund?.currency as 'fiat' | 'crypto') ||
                          'fiat'
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      Status
                    </label>
                    <Badge className={getStatusBadgeColor(paymentMatch.status)}>
                      {paymentMatch.status}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      Initiated By
                    </label>
                    <Badge
                      className={getInitiatorBadgeColor(paymentMatch.initiator)}
                    >
                      {paymentMatch.initiator}
                    </Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      Created At
                    </label>
                    <p className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      {new Date(paymentMatch.created_at).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      Updated At
                    </label>
                    <p className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      {new Date(paymentMatch.updated_at).toLocaleString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Fund Information */}
            {paymentMatch.fund && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-600">
                    <ArrowUp className="w-5 h-5" />
                    Fund Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Fund ID
                      </label>
                      <p className="font-mono">
                        #{shortenId(paymentMatch.fund.id)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Amount
                      </label>
                      <p className="text-lg font-semibold text-green-600">
                        {formatCurrencyAmount(
                          paymentMatch.fund.amount,
                          paymentMatch.fund.currency as 'fiat' | 'crypto'
                        )}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Status
                      </label>
                      <Badge variant="outline">
                        {paymentMatch.fund.status}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Created
                      </label>
                      <p>
                        {new Date(
                          paymentMatch.fund.created_at
                        ).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  {paymentMatch.fund_user && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Fund Owner
                      </label>
                      <div className="flex items-center gap-2 mt-1 p-3 bg-green-50 rounded border border-green-200">
                        <User className="w-4 h-4" />
                        <span className="font-medium">
                          {paymentMatch.fund_user.full_name}
                        </span>
                        <span className="text-gray-500">
                          ({paymentMatch.fund_user.email})
                        </span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() =>
                            navigate(
                              `/admin/users/${paymentMatch.fund_user?.id}`
                            )
                          }
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Withdraw Information */}
            {paymentMatch.withdraw && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600">
                    <ArrowDown className="w-5 h-5" />
                    Withdraw Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Withdraw ID
                      </label>
                      <p className="font-mono">
                        #{shortenId(paymentMatch.withdraw.id)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Base Amount
                      </label>
                      <p className="text-lg font-semibold text-red-600">
                        {formatCurrencyAmount(
                          paymentMatch.withdraw.base_withdrawable_amount,
                          (paymentMatch.withdraw.fund?.currency ||
                            paymentMatch.fund?.currency) as 'fiat' | 'crypto'
                        )}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Total Withdrawable
                      </label>
                      <p className="text-lg font-semibold text-red-600">
                        {formatCurrencyAmount(
                          paymentMatch.withdraw.total_withdrawable_amount,
                          (paymentMatch.withdraw.fund?.currency ||
                            paymentMatch.fund?.currency) as 'fiat' | 'crypto'
                        )}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Amount Matched
                      </label>
                      <p className="text-lg font-semibold text-green-600">
                        {formatCurrencyAmount(
                          paymentMatch.withdraw.amount_matched,
                          (paymentMatch.withdraw.fund?.currency ||
                            paymentMatch.fund?.currency) as 'fiat' | 'crypto'
                        )}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Status
                      </label>
                      <Badge variant="outline">
                        {paymentMatch.withdraw.status}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Created
                      </label>
                      <p>
                        {new Date(
                          paymentMatch.withdraw.created_at
                        ).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  {paymentMatch.withdraw_user && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Withdraw Owner
                      </label>
                      <div className="flex items-center gap-2 mt-1 p-3 bg-red-50 rounded border border-red-200">
                        <User className="w-4 h-4" />
                        <span className="font-medium">
                          {paymentMatch.withdraw_user.full_name}
                        </span>
                        <span className="text-gray-500">
                          ({paymentMatch.withdraw_user.email})
                        </span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() =>
                            navigate(
                              `/admin/users/${paymentMatch.withdraw_user?.id}`
                            )
                          }
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Payment Method Information */}
            {paymentMatch.payment_method && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    Payment Method
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Method ID
                      </label>
                      <p className="font-mono">
                        #{shortenId(paymentMatch.payment_method.id)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Type
                      </label>
                      <Badge variant="outline" className="capitalize">
                        {paymentMatch.payment_method.type}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Currency
                      </label>
                      <p className="font-semibold">
                        {paymentMatch.payment_method.currency}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Status
                      </label>
                      <Badge variant="outline" className="capitalize">
                        {paymentMatch.payment_method.status}
                      </Badge>
                    </div>
                  </div>
                  {paymentMatch.payment_method.details && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Details
                      </label>
                      <pre className="text-xs bg-brand-grey-100 p-3 rounded mt-1 overflow-x-auto border">
                        {JSON.stringify(
                          paymentMatch.payment_method.details,
                          null,
                          2
                        )}
                      </pre>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Disputes Information */}
            {paymentMatch.disputes && paymentMatch.disputes.length > 0 && (
              <Card className="border-red-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600">
                    <AlertTriangle className="w-5 h-5" />
                    Disputes Information ({paymentMatch.disputes.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {paymentMatch.disputes.map((dispute, index) => (
                    <div key={dispute.id} className={`space-y-4 ${index > 0 ? 'pt-6 border-t border-gray-200' : ''}`}>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Dispute ID
                          </label>
                          <p className="font-mono" title={dispute.id}>
                            #{shortenId(dispute.id)}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Status
                          </label>
                          <Badge variant="destructive">
                            {dispute.status}
                          </Badge>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Created By
                          </label>
                          <p>
                            {dispute.dispute_user?.full_name || 'Unknown'}
                            {dispute.dispute_user_id === paymentMatch.fund_user_id ? ' (Funder)' : ' (Withdrawer)'}
                          </p>
                        </div>
                        <div>
                          <p>
                            {new Date(dispute.disputed_at).toLocaleString()}
                          </p>
                        </div>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Reason
                        </label>
                        <p className="mt-1 p-3 bg-red-50 rounded border border-red-200">
                          {dispute.reason}
                        </p>
                      </div>

                      {dispute.resolution && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Resolution
                          </label>
                          <Badge variant="outline" className="mt-1">
                            {dispute.resolution}
                          </Badge>
                        </div>
                      )}

                      {dispute.resolution_notes && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">
                            Resolution Notes
                          </label>
                          <p className="mt-1 p-3 bg-blue-50 rounded border border-blue-200">
                            {dispute.resolution_notes}
                          </p>
                        </div>
                      )}

                      {dispute.refund_amount && (
                        <div>
                            Refund Amount
                          </label>
                          <p className="mt-1 p-3 bg-green-50 rounded border border-green-200 font-medium text-green-700">
                            {formatCurrencyAmount(
                              parseFloat(dispute.refund_amount),
                              paymentMatch.fund?.currency || 'fiat'
                            )}
                          </p>
                        </div>
                      )}

                      <div className="flex justify-end">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => navigate(`/disputes/${dispute.id}`)}
                        >
                          View Full Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Payment Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Payment Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-brand-grey-50 rounded border">
                    <span className="text-sm font-medium">
                      Payment Sent Confirmed
                    </span>
                    <div className="flex items-center gap-2">
                      {paymentMatch.is_payment_sent_confirmed ? (
                        <>
                          <Badge className="bg-green-100 text-green-800">
                            Confirmed
                          </Badge>
                          {paymentMatch.payment_sent_confirmed_at && (
                            <span className="text-xs text-gray-500">
                              {new Date(
                                paymentMatch.payment_sent_confirmed_at
                              ).toLocaleString()}
                            </span>
                          )}
                        </>
                      ) : (
                        <Badge variant="secondary">Pending</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-brand-grey-50 rounded border">
                    <span className="text-sm font-medium">
                      Payment Received Confirmed
                    </span>
                    <div className="flex items-center gap-2">
                      {paymentMatch.is_payment_received_confirmed ? (
                        <>
                          <Badge className="bg-green-100 text-green-800">
                            Confirmed
                          </Badge>
                          {paymentMatch.payment_received_confirmed_at && (
                            <span className="text-xs text-gray-500">
                              {new Date(
                                paymentMatch.payment_received_confirmed_at
                              ).toLocaleString()}
                            </span>
                          )}
                        </>
                      ) : (
                        <Badge variant="secondary">Pending</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-brand-grey-50 rounded border">
                    <span className="text-sm font-medium">Admin Withdraw</span>
                    <Badge
                      variant={
                        paymentMatch.is_admin_withdraw ? 'default' : 'secondary'
                      }
                    >
                      {paymentMatch.is_admin_withdraw ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Notes */}
            {paymentMatch.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-wrap p-3 bg-brand-grey-50 rounded border">
                    {paymentMatch.notes}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Admin Payment Actions */}
            {isAdminFund && (canMakePayment || canUploadProof) && (
              <Card className="border-brand-gold-200 bg-brand-gold-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-brand-gold-700">
                    <Send className="w-5 h-5" />
                    Admin Payment Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {canMakePayment && (
                    <div className="p-3 bg-white rounded border border-brand-gold-200">
                      <p className="text-sm text-brand-grey-600 mb-3">
                        As the admin funder, you need to send payment to the
                        withdrawer using their payment method details.
                      </p>
                      <Button
                        onClick={() => setShowAdminPaymentModal(true)}
                        className="w-full bg-brand-gold-600 hover:bg-brand-gold-700 text-white"
                        disabled={confirmPaymentMutation.isPending}
                      >
                        <Send className="w-4 h-4 mr-2" />
                        {confirmPaymentMutation.isPending
                          ? 'Processing...'
                          : 'Make Payment'}
                      </Button>
                    </div>
                  )}

                  {canUploadProof && (
                    <div className="p-3 bg-white rounded border border-brand-gold-200">
                      <p className="text-sm text-brand-grey-600 mb-3">
                        Upload proof of payment to complete the transaction.
                      </p>
                      <Button
                        onClick={() => setShowAdminProofModal(true)}
                        variant="outline"
                        className="w-full border-brand-gold-600 text-brand-gold-600 hover:bg-brand-gold-50"
                        disabled={uploadProofMutation.isPending}
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        {uploadProofMutation.isPending
                          ? 'Uploading...'
                          : 'Upload Payment Proof'}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Admin Payment Modals */}
        {paymentMatch && (
          <>
            <AdminPaymentModal
              isOpen={showAdminPaymentModal}
              onClose={() => setShowAdminPaymentModal(false)}
              paymentMatch={paymentMatch}
              onConfirm={async (transactionHash: string) => {
                confirmPaymentMutation.mutate({ transactionHash });
              }}
              loading={confirmPaymentMutation.isPending}
            />

            <AdminPaymentProofModal
              isOpen={showAdminProofModal}
              onClose={() => setShowAdminProofModal(false)}
              paymentMatch={paymentMatch}
              onConfirm={async (file: File) => {
                uploadProofMutation.mutate(file);
              }}
              loading={uploadProofMutation.isPending}
            />
          </>
        )}
      </div>
    </MainLayout>
  );
};

export default PaymentMatchDetailsPage;
