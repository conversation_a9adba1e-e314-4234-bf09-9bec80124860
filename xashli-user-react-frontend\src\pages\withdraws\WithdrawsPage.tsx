import { <PERSON>, useNavigate } from 'react-router-dom';
import { BarChart3, RefreshCw, Download, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { Pagination } from '../../components/ui';
import { WithdrawCard } from '../../components/withdraws/WithdrawCard';
import { WithdrawFilters } from '../../components/withdraws/WithdrawFilters';
import { DeactivatedUserAlert } from '../../components/ui/DeactivatedUserAlert';
import { useWithdraws } from '../../hooks/withdraws';
import { useAuth } from '../../contexts/AuthContext';

export function WithdrawsPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    withdraws,
    loading,
    filters,
    pagination,
    updateFilters,
    resetFilters: resetWithdrawFilters,
    refetch,
    goToPage,
    changePageSize,
  } = useWithdraws();

  const resetFilters = () => {
    resetWithdrawFilters();
  };

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>My Withdraws</h1>
          <p className='text-foreground-secondary mt-2'>
            Manage your withdrawal requests and track payment matches
          </p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            className='flex items-center gap-2'
            onClick={() => refetch()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link to='/withdraws/statistics' className='shrink-0'>
            <Button variant='outline' className='flex items-center gap-2'>
              <BarChart3 className='h-4 w-4' />
              Statistics
            </Button>
          </Link>
        </div>
      </div>

      {/* Deactivated User Alert */}
      {user && !user.is_active && (
        <div className='mb-6'>
          <DeactivatedUserAlert
            user={user}
            customMessage='Your account has been deactivated. You cannot create new withdrawal requests while your account is inactive.'
          />
        </div>
      )}

      {/* Filters */}
      <div className='mb-6'>
        <WithdrawFilters
          filters={filters}
          onFiltersChange={updateFilters}
          onReset={resetFilters}
        />
      </div>

      {/* Withdraws Grid */}
      {!Array.isArray(withdraws) || withdraws.length === 0 ? (
        <div className='text-center py-12'>
          <div className='mx-auto w-24 h-24 bg-background-secondary rounded-full flex items-center justify-center mb-4'>
            <Download className='h-12 w-12 text-foreground-muted' />
          </div>
          <h3 className='text-xl font-semibold text-foreground mb-2'>
            No Withdraws Found
          </h3>
          <p className='text-foreground-secondary mb-6 max-w-md mx-auto'>
            {Object.keys(filters).length > 0
              ? 'No withdraws match your current filters. Try adjusting your search criteria.'
              : 'You have not made any withdrawal requests yet. Withdraws will appear here once you request them from your matured funds.'}
          </p>
          {Object.keys(filters).length > 0 ? (
            <Button onClick={resetFilters} variant='outline'>
              Clear Filters
            </Button>
          ) : (
            <Link to='/funds'>
              <Button>
                <AlertCircle className='h-4 w-4 mr-2' />
                View My Funds
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {withdraws.map(withdraw => (
              <WithdrawCard
                key={withdraw.id}
                withdraw={withdraw}
                onView={() => navigate(`/withdraws/${withdraw.id}`)}
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination.total > 0 && (
            <div className='mt-8'>
              <Pagination
                pagination={pagination}
                currentPage={pagination.current_page}
                pageSize={pagination.per_page}
                onPageChange={goToPage}
                onPageSizeChange={changePageSize}
                isLoading={loading}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
