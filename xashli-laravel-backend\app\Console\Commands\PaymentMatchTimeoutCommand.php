<?php

namespace App\Console\Commands;

use App\Services\PaymentMatchTimeoutService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PaymentMatchTimeoutCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:payment-match-timeout';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process payment match timeouts and deactivate users who failed to fund';

    protected $paymentMatchTimeoutService;

    public function __construct(PaymentMatchTimeoutService $paymentMatchTimeoutService)
    {
        parent::__construct();
        $this->paymentMatchTimeoutService = $paymentMatchTimeoutService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting payment match timeout processing...');

        try {
            // Get statistics before processing
            $beforeStats = $this->paymentMatchTimeoutService->getTimeoutStatistics();
            
            $this->info("Current timeout statistics:");
            $this->info("- Timeout threshold: {$beforeStats['timeout_hours']} hours");
            $this->info("- Timed out matches: {$beforeStats['timed_out_matches']}");
            $this->info("- Approaching timeout: {$beforeStats['approaching_timeout_matches']}");
            $this->info("- Total pending matches: {$beforeStats['total_pending_matches']}");

            if ($beforeStats['timed_out_matches'] == 0) {
                $this->info('No timed out payment matches found. Nothing to process.');
                return 0;
            }

            // Process timeouts
            $stats = $this->paymentMatchTimeoutService->processTimeouts();

            $this->info('Payment match timeout processing completed successfully!');
            $this->info("Processing statistics:");
            $this->info("- Processed matches: {$stats['processed_matches']}");
            $this->info("- Deactivated users: {$stats['deactivated_users']}");
            $this->info("- Cancelled matches: {$stats['cancelled_matches']}");
            $this->info("- Updated funds: {$stats['updated_funds']}");
            $this->info("- Updated withdraws: {$stats['updated_withdraws']}");

            return 0;

        } catch (\Exception $e) {
            $this->error('Error in payment match timeout processing: ' . $e->getMessage());
            Log::error('Payment match timeout process error: ' . $e->getMessage());

            return 1;
        }
    }
}
