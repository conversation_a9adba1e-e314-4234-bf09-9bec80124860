import type { PaginationMeta } from './common';

export interface PaymentMatchDetails {
  id: string;
  fund_id: string;
  withdraw_id: string;
  amount: string;
  payment_method_id: string;
  initiator: 'auto' | 'manual';
  status: 'pending' | 'paid' | 'confirmed' | 'disputed';
  notes?: string;
  payment_proof_image?: string;
  created_at: string;
  updated_at: string;
  is_payment_sent_confirmed?: boolean;
  payment_sent_confirmed_at?: string;
  is_payment_received_confirmed?: boolean;
  payment_received_confirmed_at?: string;
  is_admin_withdraw?: boolean;
  fund?: {
    id: string;
    amount: string;
    currency: string;
    status: string;
    created_at: string;
  };
  withdraw?: {
    id: string;
    amount: string;
    base_withdrawable_amount: string;
    total_withdrawable_amount: string;
    amount_matched: string;
    status: string;
    created_at: string;
    fund?: {
      id: string;
      currency: string;
    };
  };
  payment_method?: {
    id: string;
    type: string;
    currency: string;
    status: string;
    details: Record<string, any>;
    bank_name?: string;
    account_number?: string;
    account_name?: string;
    wallet_address?: string;
    crypto_network?: string;
  };
  disputes?: {
    id: string;
    payment_match_id: string;
    dispute_user_id: string;
    status: string;
    reason: string;
    resolution?: string;
    disputed_at: string;
    resolved_at?: string;
    created_at: string;
    updated_at: string;
    dispute_user?: {
      id: string;
      full_name: string;
      email: string;
    };
    resolve_user?: {
      id: string;
      full_name: string;
      email: string;
    };
  }[];
  fund_user?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
  withdraw_user?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
}

export interface PaymentMatchFilters {
  status?: string;
  initiator?: string;
  currency?: string;
  min_amount?: number;
  max_amount?: number;
  date_from?: string;
  date_to?: string;
  user_id?: string;
  fund_id?: string;
  withdraw_id?: string;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  per_page?: number;
  page?: number;
}

export interface PaymentMatchResponse {
  paymentMatches: PaymentMatchDetails[];
  pagination: PaginationMeta;
}

export interface PaymentMatchStatistics {
  overview: {
    total_count: number;
    count: {
      fiat: number;
      crypto: number;
    };
    amount: {
      fiat: number;
      crypto: number;
    };
  };
  statuses: {
    pending: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    paid: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    confirmed: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
    disputed: {
      total_count: number;
      count: {
        fiat: number;
        crypto: number;
      };
      amount: {
        fiat: number;
        crypto: number;
      };
    };
  };
  initiators: {
    auto_count: number;
    manual_count: number;
  };
  disputes: {
    total_count: number;
    pending_count: number;
    under_review_count: number;
    resolved_count: number;
  };
}

export interface ManualMatchRequest {
  fund_id: string;
  withdraw_id: string;
  amount: number;
}

export interface PaymentMatchStatisticsFilters {
  user_id?: string;
  email?: string;
  date_from?: string;
  date_to?: string;
}
