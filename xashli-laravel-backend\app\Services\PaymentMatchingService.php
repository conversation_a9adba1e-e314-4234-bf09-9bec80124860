<?php

namespace App\Services;

use App\Models\Fund;
use App\Models\PaymentMatch;
use App\Models\User;
use App\Models\Withdraw;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Payment Matching Service
 *
 * This service handles the automatic matching of user funds with user withdrawals.
 *
 * Key Features:
 * - Prevents self-matching: Users cannot match their own funds with their own withdrawals
 * - Priority system: Admin fee withdrawals get matched first with user funds only
 * - Fallback to admin funds: When user funds are insufficient, admin funds are created
 * - FIFO processing: First-in, first-out matching based on creation time
 */
class PaymentMatchingService
{
    /**
     * Execute the payment matching process
     *
     * @return int Number of matches created
     *
     * @throws \Exception
     */
    public function executeMatching(): int
    {
        $matchesCreated = 0;

        try {
            DB::beginTransaction();

            // Step 1: Process admin fee withdrawals first (highest priority - user funds only)
            $matchesCreated = $this->processAdminFeesToUserPaymentMatches();

            // Step 2: Process regular user-to-user matches
            $matchesCreated += $this->processUserToUserPaymentMatches();

            // Step 3: Handle excess funds by creating admin account withdraws
            // This handles cases where there are more funds than withdraws,
            // including both unmatched and partially matched funds
            $matchesCreated += $this->processUnmatchedFundsWithAdminPaymentMatches();

            // Note: Excess withdraws are already handled in processUserToUserPaymentMatches()
            // through createMatchWithAdminAccountFund when user funds are insufficient

            DB::commit();

            return $matchesCreated;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment matching process error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process admin fee withdrawals with user funds only (highest priority)
     * Admin fees are matched exclusively with user funds to prevent circular fee collection
     */
    protected function processAdminFeesToUserPaymentMatches(): int
    {
        $matchesCreated = 0;

        $adminFeeWithdraws = $this->getAdminFeeWithdraws();
        Log::info("Found {$adminFeeWithdraws->count()} admin fee withdrawals to process...");

        foreach ($adminFeeWithdraws as $withdraw) {
            $remainingAmount = $this->getRemainingWithdrawAmount($withdraw);
            if ($remainingAmount <= 0) {
                continue;
            }
            Log::info("Processing admin fee withdraw ID: {$withdraw->id}, Amount needed: {$remainingAmount}");

            // Only match admin fee withdrawals with USER funds (never admin funds)
            $matchesCreated += $this->processWithdrawWithUsersFundsOnly($withdraw, $remainingAmount);

            // If admin fee withdrawal can't be fully matched with user funds, leave it unmatched
            if ($remainingAmount > 0) {
                Log::warning("Admin fee withdrawal {$withdraw->id} couldn't be fully matched with user funds. Remaining: {$remainingAmount}");
            }
        }

        return $matchesCreated;
    }

    /**
     * Process regular user withdrawals with full matching capabilities
     * Includes fallback to admin funds when user funds are insufficient
     */
    protected function processUserToUserPaymentMatches(): int
    {
        $matchesCreated = 0;

        $userWithdraws = $this->getUsersWithdraws();
        Log::info("Found {$userWithdraws->count()} regular user withdrawals to process...");

        foreach ($userWithdraws as $withdraw) {
            $remainingAmount = $this->getRemainingWithdrawAmount($withdraw);
            if ($remainingAmount <= 0) {
                continue;
            }
            Log::info("Processing user withdraw ID: {$withdraw->id}, Amount needed: {$remainingAmount}");

            // First try to match with user funds
            $matchesCreated += $this->processWithdrawWithUsersFunds($withdraw, $remainingAmount);

            // If still unmatched, create admin fund (only for regular user withdrawals)
            if ($remainingAmount > 0) {
                Log::info("Creating admin account fund for remaining amount: {$remainingAmount}");
                $matchesCreated += $this->createMatchWithAdminAccountFund($withdraw, $remainingAmount);
            }
        }

        return $matchesCreated;
    }

    /**
     * Process unmatched user funds by creating admin account withdrawals and payment matches
     *
     * This method handles excess user funds (funds that couldn't be matched with user withdrawals)
     * by creating admin account withdrawals and matching them with the unmatched user funds.
     * This ensures all user funds get matched and users can receive their payments.
     */
    protected function processUnmatchedFundsWithAdminPaymentMatches(): int
    {
        $matchesCreated = 0;

        // Get both unmatched and partially matched funds
        $pendingFunds = Fund::where('status', 'pending')
            ->whereHas('user', function ($query) {
                $query->where('role', '!=', 'admin');
            })
            ->with(['user', 'paymentMethod'])
            ->orderBy('created_at', 'asc')
            ->get();

        Log::info('Found ' . $pendingFunds->count() . ' pending funds to process.');

        foreach ($pendingFunds as $fund) {
            $remainingAmount = $fund->amount - $fund->amount_matched;
            if ($remainingAmount > 0) {
                $matchesCreated += $this->createMatchWithAdminAccountWithdraw($fund, $remainingAmount);
            }
        }

        return $matchesCreated;
    }

    /**
     * Get admin fee withdrawals (withdrawals that have a platform fee record)
     */
    protected function getAdminFeeWithdraws()
    {
        return Withdraw::where(function ($query) {
            $query->where('status', 'pending')
                ->orWhere(function ($q) {
                    $q->where('status', 'matched')
                        ->whereRaw('amount_matched < total_withdrawable_amount');
                });
        })
            ->whereHas('platformFee') // Only withdrawals that have a platform fee record
            ->with(['user', 'fund', 'paymentMethod', 'platformFee'])
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Get regular user withdrawals (withdrawals that don't have a platform fee record)
     */
    protected function getUsersWithdraws()
    {
        return Withdraw::where(function ($query) {
            $query->where('status', 'pending')
                ->orWhere(function ($q) {
                    $q->where('status', 'matched')
                        ->whereRaw('amount_matched < total_withdrawable_amount');
                });
        })
            ->whereDoesntHave('platformFee') // Only withdrawals that don't have a platform fee record
            ->with(['user', 'fund', 'paymentMethod'])
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Get the remaining amount needed to fully match a withdrawal
     */
    protected function getRemainingWithdrawAmount(Withdraw $withdraw): float
    {
        return max(0, $withdraw->total_withdrawable_amount - $withdraw->amount_matched);
    }

    /**
     * Process a withdraw with user funds
     */
    protected function processWithdrawWithUsersFunds(Withdraw $withdraw, float &$remainingAmount): int
    {
        $matchesCreated = 0;

        $matchingFunds = Fund::where(function ($query) {
            $query->where('status', 'pending')
                ->orWhere(function ($q) {
                    $q->where('status', 'matched')
                        ->whereRaw('amount > amount_matched');
                });
        })
            ->where('currency', $withdraw->fund->currency)
            ->whereHas('user', function ($query) {
                $query->where('role', '!=', 'admin');
            })
            // Prevent self-matching: exclude funds from the same user as the withdrawal
            ->where('user_id', '!=', $withdraw->user_id)
            ->orderBy('created_at', 'asc')
            ->get();

        foreach ($matchingFunds as $fund) {
            if ($remainingAmount <= 0) {
                break;
            }

            $availableAmount = $fund->amount - $fund->amount_matched;
            $matchAmount = min($availableAmount, $remainingAmount);

            if ($this->createPaymentMatch($fund, $withdraw, $matchAmount)) {
                $remainingAmount -= $matchAmount;
                $matchesCreated++;
            }
        }

        return $matchesCreated;
    }

    /**
     * Process a withdraw with user funds only (excludes admin funds)
     * Used for admin fee withdrawals to prevent circular platform fee collection
     */
    protected function processWithdrawWithUsersFundsOnly(Withdraw $withdraw, float &$remainingAmount): int
    {
        $matchesCreated = 0;

        $matchingFunds = Fund::where(function ($query) {
            $query->where('status', 'pending')
                ->orWhere(function ($q) {
                    $q->where('status', 'matched')
                        ->whereRaw('amount > amount_matched');
                });
        })
            ->where('currency', $withdraw->fund->currency)
            ->whereHas('user', function ($query) {
                $query->where('role', '!=', 'admin');
            })
            // Prevent self-matching: exclude funds from the same user as the withdrawal
            ->where('user_id', '!=', $withdraw->user_id)
            ->orderBy('created_at', 'asc')
            ->get();

        foreach ($matchingFunds as $fund) {
            if ($remainingAmount <= 0) {
                break;
            }

            $availableAmount = $fund->amount - $fund->amount_matched;
            $matchAmount = min($availableAmount, $remainingAmount);

            if ($this->createPaymentMatch($fund, $withdraw, $matchAmount)) {
                $remainingAmount -= $matchAmount;
                $matchesCreated++;
            }
        }

        return $matchesCreated;
    }

    /**
     * Creates a payment match between a withdraw and a newly created admin account fund
     */
    protected function createMatchWithAdminAccountFund(Withdraw $withdraw, float $amount): int
    {
        $fund = $this->createAdminAccountFund($withdraw, $amount);
        if (! $fund) {
            return 0;
        }

        return $this->createPaymentMatch($fund, $withdraw, $amount) ? 1 : 0;
    }

    /**
     * Creates a payment match between a fund and a newly created admin account withdraw
     */
    protected function createMatchWithAdminAccountWithdraw(Fund $fund, float $amount): int
    {
        $withdraw = $this->createAdminAccountWithdraw($fund, $amount);
        if (! $withdraw) {
            return 0;
        }

        return $this->createPaymentMatch($fund, $withdraw, $amount) ? 1 : 0;
    }

    /**
     * Create a fund from an admin account
     */
    protected function createAdminAccountFund(Withdraw $withdraw, float $amount): ?Fund
    {
        $adminAccount = User::getAdminAccount($withdraw->fund->currency);
        if (! $adminAccount) {
            Log::warning("No available admin account for {$withdraw->fund->currency} funding.");

            return null;
        }

        try {
            $fund = Fund::create([
                'user_id' => $adminAccount->id,
                'amount' => $amount,
                'currency' => $withdraw->fund->currency,
                'payment_method_id' => $adminAccount->paymentMethods->where('type', $withdraw->fund->currency === 'fiat' ? 'bank' : 'crypto')->first()->id,
                'status' => 'pending',
                // Admin funds don't generate platform fees, so set to 0
                'platform_fee_percentage' => 0.00,
                // Admin funds don't have maturity/growth features
                'maturity_days' => 0,
                'growth_percentage' => 0.00,
                'growth_amount' => 0.00,
                'referral_bonus_limit' => 0.00,
                // Admin funds are immediately eligible for withdrawal matching
                'is_eligible_for_withdrawal' => true,
            ]);

            // Track admin account funding in stats
            if ($adminAccount->stats) {
                if ($withdraw->fund->currency === 'fiat') {
                    $adminAccount->stats->incrementTotalFiatFunded($amount);
                } else {
                    $adminAccount->stats->incrementTotalCryptoFunded($amount);
                }
            }

            // Log admin account usage
            Log::info("Admin account {$adminAccount->id} created fund of {$amount} {$withdraw->fund->currency}");

            return $fund;
        } catch (\Exception $e) {
            Log::error('Error creating admin account fund: ' . $e->getMessage());

            return null;
        }
    }

    /**
     * Create a withdraw from an admin account
     */
    protected function createAdminAccountWithdraw(Fund $fund, float $amount): ?Withdraw
    {
        $adminAccount = User::getAdminAccount($fund->currency);
        if (! $adminAccount) {
            Log::warning("No available admin account for {$fund->currency} withdrawal.");

            return null;
        }

        try {
            // Use existing payment methods based on currency type
            $paymentMethod = $adminAccount->paymentMethods()->where('type', $fund->currency === 'fiat' ? 'bank' : 'crypto')->where('status', 'active')->first();

            if (! $paymentMethod) {
                Log::warning('No active ' . ($fund->currency === 'fiat' ? 'bank' : 'crypto') . " payment method found for admin account {$adminAccount->id}");

                return null;
            }

            $withdraw = Withdraw::create([
                'user_id' => $adminAccount->id,
                'fund_id' => $fund->id,
                'base_withdrawable_amount' => $amount,
                'available_referral_bonus' => 0,
                'withdrawable_referral_bonus' => 0,
                'total_withdrawable_amount' => $amount,
                'amount_matched' => 0,
                'payment_method_id' => $paymentMethod->id,
                'status' => 'pending',
            ]);

            // Track admin account withdrawal in stats
            if ($adminAccount->stats) {
                if ($fund->currency === 'fiat') {
                    $adminAccount->stats->incrementTotalFiatWithdrawn($amount);
                } else {
                    $adminAccount->stats->incrementTotalCryptoWithdrawn($amount);
                }
            }

            // Log admin account usage
            $accountType = $fund->currency === 'fiat' ? 'existing bank' : 'existing crypto wallet';
            Log::info("Admin account {$adminAccount->id} created withdraw of {$amount} {$fund->currency} using {$accountType} account");

            return $withdraw;
        } catch (\Exception $e) {
            Log::error('Error creating admin account withdraw: ' . $e->getMessage());

            return null;
        }
    }

    /**
     * Create a payment match between a fund and withdraw
     */
    protected function createPaymentMatch(Fund $fund, Withdraw $withdraw, float $amount): bool
    {
        try {
            // Determine if this is an admin withdrawal
            $isAdminWithdraw = $withdraw->user->role === 'admin';

            $match = PaymentMatch::create([
                'fund_id' => $fund->id,
                'withdraw_id' => $withdraw->id,
                'fund_user_id' => $fund->user_id,
                'withdraw_user_id' => $withdraw->user_id,
                'amount' => $amount,
                'payment_method_id' => $withdraw->payment_method_id,
                'initiator' => 'auto',
                'status' => 'pending',
                'is_admin_withdraw' => $isAdminWithdraw,
            ]);

            // Update fund status
            $fund->amount_matched += $amount;
            if ($fund->amount_matched >= $fund->amount) {
                $fund->status = 'matched';
            }
            $fund->save();

            // Update withdraw status
            $withdraw->amount_matched += $amount;
            if ($withdraw->amount_matched >= $withdraw->total_withdrawable_amount) {
                $withdraw->status = 'matched';
            }
            $withdraw->save();

            Log::info("Created match {$match->id}: Fund {$fund->id} -> Withdraw {$withdraw->id}, Amount: {$amount}");

            return true;
        } catch (\Exception $e) {
            Log::error('Error creating match: ' . $e->getMessage(), [
                'fund_id' => $fund->id,
                'withdraw_id' => $withdraw->id,
                'amount' => $amount,
            ]);

            return false;
        }
    }
}
