import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, TrendingUp, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/Select';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { DeactivatedUserAlert } from '../../components/ui/DeactivatedUserAlert';
import { useFundForm } from '../../hooks/funds';
import { usePaymentMethods } from '../../hooks/payment-methods';
import { useAuth } from '../../contexts/AuthContext';
import type { CreateFundData } from '../../types';

interface FormErrors {
  amount?: string;
  currency?: string;
  payment_method_id?: string;
}

export function CreateFundPage() {
  const { user } = useAuth();
  const { loading, createFund } = useFundForm();
  const { paymentMethods, loading: paymentMethodsLoading } =
    usePaymentMethods();

  const [formData, setFormData] = useState<CreateFundData>({
    amount: 0,
    currency: 'fiat',
    payment_method_id: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Filter payment methods based on selected currency
  const availablePaymentMethods = paymentMethods.filter(pm => {
    if (formData.currency === 'fiat') {
      return pm.type === 'bank';
    }
    return pm.type === 'crypto';
  });

  const handleInputChange = (
    field: keyof CreateFundData,
    value: string | number
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount is required and must be greater than 0';
    }

    if (!formData.currency) {
      newErrors.currency = 'Currency is required';
    }

    if (!formData.payment_method_id) {
      newErrors.payment_method_id = 'Payment method is required';
    }

    // Validate amount multiples
    if (formData.amount > 0) {
      const fiatStep = 10;
      const cryptoStep = 0.025;
      const step = formData.currency === 'fiat' ? fiatStep : cryptoStep;

      if (formData.currency === 'crypto') {
        const amountInSmallestUnit = Math.round(formData.amount * 1000);
        const stepInSmallestUnit = Math.round(cryptoStep * 1000);
        if (amountInSmallestUnit % stepInSmallestUnit !== 0) {
          newErrors.amount = `Crypto amount must be in multiples of ${cryptoStep} SOL`;
        }
      } else {
        if (formData.amount % step !== 0) {
          newErrors.amount = `Fiat amount must be in multiples of ₦${fiatStep}`;
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await createFund(formData);
  };
  const getAmountHelperText = () => {
    if (formData.currency === 'fiat') {
      return 'Amount must be in multiples of ₦10 (e.g., ₦10,000, ₦15,000, ₦20,000)';
    }
    return 'Amount must be in multiples of 0.025 SOL (e.g., 0.025, 0.05, 0.1)';
  };

  const getAmountPlaceholder = () => {
    if (formData.currency === 'fiat') {
      return 'Enter amount in Naira (e.g., 10000)';
    }
    return 'Enter amount in SOL (e.g., 0.1)';
  };

  if (paymentMethodsLoading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8 max-w-2xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link to='/funds'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='h-4 w-4' />
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>
            Create New Fund
          </h1>
          <p className='text-foreground-secondary mt-2'>
            Start a new investment fund to grow your money
          </p>
        </div>
      </div>

      {/* Deactivated User Alert */}
      {user && !user.is_active && (
        <div className='mb-6'>
          <DeactivatedUserAlert
            user={user}
            customMessage='Your account has been deactivated. You cannot create new funds while your account is inactive.'
          />
        </div>
      )}

      {/* Info Card */}
      <div className='mb-8'>
        <div className='bg-blue-950/20 border border-blue-800/30 rounded-lg p-4'>
          <div className='flex items-center gap-3 mb-2'>
            <TrendingUp className='h-5 w-5 text-blue-400' />
            <h3 className='font-medium text-blue-200'>Growth Rates</h3>
          </div>
          <p className='text-sm text-blue-300'>
            Fiat (Naira) investments earn{' '}
            <span className='font-semibold text-blue-200'>50% growth rate</span>
            , while Crypto (Solana) investments earn{' '}
            <span className='font-semibold text-blue-200'>55% growth rate</span>
            .
          </p>
        </div>
      </div>
      {/* Form */}
      <div className='bg-background-secondary rounded-xl border border-border p-6'>
        <form onSubmit={handleSubmit} className='space-y-6'>
          {/* Currency Selection */}
          <div>
            <label className='block text-sm font-medium text-foreground-secondary mb-2'>
              Currency Type
            </label>
            <Select
              value={formData.currency}
              onValueChange={value => {
                handleInputChange('currency', value);
                // Reset payment method when currency changes
                setFormData(prev => ({ ...prev, payment_method_id: '' }));
              }}
            >
              {' '}
              <SelectTrigger>
                {formData.currency ? (
                  formData.currency === 'fiat' ? (
                    'Fiat (Nigerian Naira)'
                  ) : (
                    'Crypto (Solana)'
                  )
                ) : (
                  <SelectValue placeholder='Select currency type' />
                )}
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='fiat'>Fiat (Nigerian Naira)</SelectItem>
                <SelectItem value='crypto'>Crypto (Solana)</SelectItem>
              </SelectContent>
            </Select>{' '}
            {errors.currency && (
              <p className='text-destructive text-sm mt-1'>{errors.currency}</p>
            )}
          </div>
          {/* Amount */}
          <div>
            <label className='block text-sm font-medium text-foreground-secondary mb-2'>
              Amount
            </label>{' '}
            <div className='relative'>
              <div className='absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted font-medium text-sm'>
                {formData.currency === 'fiat' ? '₦' : 'SOL'}
              </div>
              <Input
                type='number'
                placeholder={getAmountPlaceholder()}
                value={formData.amount || ''}
                onChange={e =>
                  handleInputChange('amount', parseFloat(e.target.value) || 0)
                }
                onWheel={e => e.currentTarget.blur()}
                step={formData.currency === 'fiat' ? '10' : '0.025'}
                min={formData.currency === 'fiat' ? '10' : '0.025'}
                className={formData.currency === 'fiat' ? 'pl-8' : 'pl-12'}
                required
              />
            </div>
            <p className='text-xs text-foreground-secondary mt-1'>
              {getAmountHelperText()}
            </p>{' '}
            {errors.amount && (
              <p className='text-destructive text-sm mt-1'>{errors.amount}</p>
            )}
          </div>{' '}
          {/* Payment Method */}
          <div>
            <label className='block text-sm font-medium text-foreground-secondary mb-2'>
              Payment Method
            </label>
            <div className='bg-blue-950/30 border border-blue-800/40 rounded-lg p-3 mb-3'>
              <p className='text-xs text-blue-200 flex items-start gap-2'>
                <AlertCircle className='h-3 w-3 mt-0.5 text-blue-400 flex-shrink-0' />
                This payment method will be used when you withdraw your funds
                and profits from this investment.
              </p>
            </div>
            <Select
              value={formData.payment_method_id}
              onValueChange={value =>
                handleInputChange('payment_method_id', value)
              }
            >
              {' '}
              <SelectTrigger>
                {formData.payment_method_id ? (
                  (() => {
                    const selectedPM = availablePaymentMethods.find(
                      pm => pm.id === formData.payment_method_id
                    );
                    if (selectedPM) {
                      return selectedPM.type === 'bank'
                        ? `${selectedPM.bank_name} - ${selectedPM.account_number}`
                        : `${selectedPM.crypto_network} - ${selectedPM.wallet_address?.slice(0, 12)}...${selectedPM.wallet_address?.slice(-4)}`;
                    }
                    return 'Select a payment method';
                  })()
                ) : (
                  <SelectValue
                    placeholder={
                      availablePaymentMethods.length === 0
                        ? `No ${formData.currency} payment methods available`
                        : 'Select a payment method'
                    }
                  />
                )}
              </SelectTrigger>
              <SelectContent>
                {availablePaymentMethods.map(pm => (
                  <SelectItem key={pm.id} value={pm.id}>
                    <div className='flex flex-col py-1'>
                      {pm.type === 'bank' ? (
                        <>
                          <span className='font-medium'>{pm.bank_name}</span>
                          <span className='text-sm text-foreground-secondary'>
                            {pm.account_number} • {pm.account_name}
                          </span>
                        </>
                      ) : (
                        <>
                          <span className='font-medium'>
                            {pm.crypto_network} Network
                          </span>
                          <span className='text-sm text-foreground-secondary font-mono'>
                            {pm.wallet_address?.slice(0, 16)}...
                            {pm.wallet_address?.slice(-6)}
                          </span>
                        </>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>{' '}
            {availablePaymentMethods.length === 0 && (
              <p className='text-warning text-sm mt-1'>
                You need to add a {formData.currency} payment method first.{' '}
                <Link to='/payment-methods/create' className='underline'>
                  Add payment method
                </Link>
              </p>
            )}
            {errors.payment_method_id && (
              <p className='text-destructive text-sm mt-1'>
                {errors.payment_method_id}
              </p>
            )}
          </div>
          {/* Submit Button */}
          <div className='flex gap-4 pt-4'>
            <Link to='/funds' className='flex-1'>
              <Button variant='outline' className='w-full'>
                Cancel
              </Button>
            </Link>
            <Button
              type='submit'
              className='flex-1'
              disabled={
                loading ||
                availablePaymentMethods.length === 0 ||
                (user && !user.is_active)
              }
            >
              {loading ? (
                <>
                  <LoadingSpinner size='sm' className='mr-2' />
                  Creating Fund...
                </>
              ) : (
                'Create Fund'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
