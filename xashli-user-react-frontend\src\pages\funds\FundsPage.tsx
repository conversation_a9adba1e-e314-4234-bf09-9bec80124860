import { Link, useNavigate } from 'react-router-dom';
import { Plus, TrendingUp, BarChart3, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { Pagination } from '../../components/ui';
import { FundCard } from '../../components/funds/FundCard';
import { FundFilters } from '../../components/funds/FundFilters';
import { DeactivatedUserAlert } from '../../components/ui/DeactivatedUserAlert';
import { useFunds } from '../../hooks/funds';
import { useAuth } from '../../contexts/AuthContext';

export function FundsPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    funds,
    pagination,
    loading,
    filters,
    applyFilters,
    handlePageChange,
    handlePageSizeChange,
  } = useFunds();

  const resetFilters = () => {
    applyFilters({});
  };

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>My Funds</h1>
          <p className='text-foreground-secondary mt-2'>
            Manage your investments and track their performance
          </p>
        </div>
        <div className='flex gap-2'>
          <Link to='/funds/statistics' className='shrink-0'>
            <Button variant='outline' className='flex items-center gap-2'>
              <BarChart3 className='h-4 w-4' />
              Statistics
            </Button>
          </Link>
          <Link to='/funds/create' className='shrink-0'>
            <Button
              className='flex items-center gap-2'
              disabled={user && !user.is_active}
            >
              <Plus className='h-4 w-4' />
              Create Fund
            </Button>
          </Link>
        </div>
      </div>

      {/* Deactivated User Alert */}
      {user && !user.is_active && (
        <div className='mb-6'>
          <DeactivatedUserAlert
            user={user}
            customMessage='Your account has been deactivated. You cannot create new funds while your account is inactive.'
          />
        </div>
      )}

      {/* Phone Number Reminder for Active Users */}
      {user && user.is_active && (!user.phone || user.phone.trim() === '') && (
        <div className='mb-6'>
          <div className='bg-warning/10 border border-warning/30 rounded-lg p-4'>
            <div className='flex items-start gap-3'>
              <AlertCircle className='h-5 w-5 text-warning flex-shrink-0 mt-0.5' />
              <div>
                <h3 className='font-medium text-warning mb-1'>Add Your Phone Number</h3>
                <p className='text-sm text-foreground-secondary'>
                  Add your phone number to your profile to ensure smooth communication during funding processes.
                  This helps avoid payment delays and potential account issues.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className='mb-6'>
        <FundFilters
          filters={filters}
          onFiltersChange={applyFilters}
          onReset={resetFilters}
        />
      </div>

      {/* Funds Grid */}
      {funds.length === 0 ? (
        <div className='text-center py-12'>
          <div className='mx-auto w-24 h-24 bg-background-secondary rounded-full flex items-center justify-center mb-4'>
            <TrendingUp className='h-12 w-12 text-foreground-muted' />
          </div>
          <h3 className='text-xl font-semibold text-foreground mb-2'>
            No Funds Found
          </h3>
          <p className='text-foreground-secondary mb-6 max-w-md mx-auto'>
            {Object.keys(filters).length > 0
              ? 'No funds match your current filters. Try adjusting your search criteria.'
              : 'Create your first fund to start growing your investment portfolio.'}
          </p>
          {Object.keys(filters).length > 0 ? (
            <Button onClick={resetFilters} variant='outline'>
              Clear Filters
            </Button>
          ) : (
            <Link to='/funds/create'>
              <Button>
                <Plus className='h-4 w-4 mr-2' />
                Create Your First Fund
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {funds.map(fund => (
              <FundCard
                key={fund.id}
                fund={fund}
                onView={() => navigate(`/funds/${fund.id}`)}
              />
            ))}
          </div>

          {pagination && pagination.total > 0 && (
            <Pagination
              pagination={pagination}
              currentPage={pagination.current_page}
              pageSize={pagination.per_page}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              itemLabel='Funds'
              className='mt-8'
            />
          )}
        </>
      )}
    </div>
  );
}
