<?php

namespace App\Http\Controllers;

use App\Models\AdminActivityLog;
use App\Models\PaymentDispute;
use App\Models\PaymentMatch;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PaymentDisputeController extends Controller
{
    /**
     * Display a listing of payment disputes.
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();

        if ($user->isAdmin()) {
            $query = PaymentDispute::with([
                'paymentMatch.fund:id,currency,amount,user_id',
                'paymentMatch.fund.user:id,full_name,email',
                'paymentMatch.withdraw.user:id,full_name,email',
                'disputeUser:id,full_name,email',
                'resolveUser:id,full_name,email',
            ]);
        } else {
            // Users can only see their own disputes
            $query = PaymentDispute::with([
                'paymentMatch.fund:id,currency,amount,user_id',
                'paymentMatch.fund.user:id,full_name,email',
                'paymentMatch.withdraw.user:id,full_name,email',
                'disputeUser:id,full_name,email',
                'resolveUser:id,full_name,email',
            ])->byUser($user->id);
        }        // Filter by status if provided
        if ($request->has('status') && in_array($request->status, ['under_review', 'resolved', 'rejected'])) {
            $query->where('status', $request->status);
        }

        // Filter by resolution if provided (admin only)
        if ($user->isAdmin() && $request->has('resolution') && in_array($request->resolution, ['confirmed', 'cancelled', 'partial_refund'])) {
            $query->where('resolution', $request->resolution);
        }

        // Filter by user (admin only)
        if ($user->isAdmin() && $request->has('user_id')) {
            $query->byUser($request->user_id);
        }

        // Filter by admin (admin only)
        if ($user->isAdmin() && $request->has('admin_id')) {
            $query->resolvedByAdmin($request->admin_id);
        }

        // Date range filter
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->withinDateRange($request->start_date, $request->end_date);
        }

        // Search functionality
        if ($request->has('search') && ! empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('reason', 'like', "%{$searchTerm}%")
                    ->orWhere('resolution_notes', 'like', "%{$searchTerm}%");
            });
        }

        // Sort by disputed_at in descending order (newest first)
        $query->orderBy('disputed_at', 'desc');

        // Pagination
        $isAdmin = $user->isAdmin();
        $perPage = min($request->get('per_page', 15), $isAdmin ? 100 : 50);
        $paginatedDisputes = $query->paginate($perPage);

        // Structure response with separate disputes and pagination
        $response = [
            'disputes' => $paginatedDisputes->items(),
            'pagination' => [
                'current_page' => $paginatedDisputes->currentPage(),
                'last_page' => $paginatedDisputes->lastPage(),
                'per_page' => $paginatedDisputes->perPage(),
                'total' => $paginatedDisputes->total(),
                'from' => $paginatedDisputes->firstItem(),
                'to' => $paginatedDisputes->lastItem(),
            ],
        ];

        return $this->success($response, 'Payment disputes retrieved successfully');
    }

    /**
     * Display the specified payment dispute.
     */
    public function show(string $id): JsonResponse
    {
        $user = auth()->user();

        $query = PaymentDispute::with([
            'paymentMatch.fund:id,currency,amount,user_id',
            'paymentMatch.fund.user:id,full_name,email',
            'paymentMatch.withdraw.user:id,full_name,email',
            'disputeUser:id,full_name,email',
            'resolveUser:id,full_name,email',
        ]);

        // Apply user restriction for non-admin users
        if (! $user->isAdmin()) {
            $query->byUser($user->id);
        }

        $dispute = $query->find($id);

        if (! $dispute) {
            return $this->notFound('Payment dispute not found');
        }

        // Double-check security: ensure non-admin users can only see their own disputes
        if (! $user->isAdmin() && $dispute->dispute_user_id !== $user->id) {
            return $this->forbidden('You do not have permission to view this dispute');
        }

        return $this->success($dispute, 'Payment dispute retrieved successfully');
    }

    /**
     * Get disputes for a specific payment match.
     */
    public function getByMatch(string $matchId): JsonResponse
    {
        $match = PaymentMatch::find($matchId);

        if (!$match) {
            return $this->notFound('Payment match not found');
        }

        // Check if the user is involved in this match
        $userId = auth()->id();
        $isUserInvolved = ($match->fund_user_id === $userId || $match->withdraw_user_id === $userId);

        if (!$isUserInvolved && !auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to view disputes for this match');
        }

        $disputes = PaymentDispute::where('payment_match_id', $matchId)
            ->with(['disputeUser:id,full_name,email'])
            ->orderBy('disputed_at', 'desc')
            ->get();

        return $this->success($disputes, 'Disputes retrieved successfully');
    }

    /**
     * Create a new payment dispute.
     */
    public function store(Request $request, string $matchId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $match = PaymentMatch::with(['withdraw.user', 'fund.user'])->find($matchId);

        if (! $match) {
            return $this->notFound('Payment match not found');
        }

        // Check if the match belongs to the authenticated user (either fund or withdraw side)
        $userId = auth()->id();
        $isUserInvolved = ($match->fund_user_id === $userId || $match->withdraw_user_id === $userId);

        if (!$isUserInvolved && !auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to dispute this match');
        }

        // Check if the match is in a disputable state
        if ($match->status !== 'paid') {
            return $this->error('Only paid matches can be disputed', 400);
        }

        // Check if the user already has an unresolved dispute for this match
        $existingUserDispute = PaymentDispute::where('payment_match_id', $matchId)
            ->where('dispute_user_id', $userId)
            ->where('status', 'under_review')
            ->first();

        if ($existingUserDispute) {
            return $this->error('You already have an unresolved dispute for this payment match', 400);
        }

        try {
            DB::beginTransaction();

            // Update match status to disputed if not already disputed
            if ($match->status !== 'disputed') {
                $match->status = 'disputed';
                $match->save();
            }
            // Create the dispute record
            $dispute = PaymentDispute::create([
                'payment_match_id' => $match->id,
                'dispute_user_id' => auth()->id(),
                'fund_user_id' => $match->fund_user_id,
                'withdraw_user_id' => $match->withdraw_user_id,
                'reason' => $request->reason,
                'status' => 'under_review',
                'disputed_at' => now(),
            ]);

            DB::commit();

            return $this->success(
                $dispute->load(['paymentMatch', 'disputeUser']),
                'Payment dispute created successfully. An admin will review your case.',
                201
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to create payment dispute: ' . $e->getMessage());
        }
    }

    /**
     * Resolve a payment dispute (admin only).
     */
    public function resolve(Request $request, string $id): JsonResponse
    {
        if (! auth()->user()->isAdmin()) {
            return $this->forbidden('Only admins can resolve disputes');
        }

        $validator = Validator::make($request->all(), [
            'resolution' => 'required|in:confirmed,cancelled,partial_refund',
            'resolution_notes' => 'required|string|max:1000',
            'refund_amount' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $dispute = PaymentDispute::with(['paymentMatch.withdraw', 'paymentMatch.fund'])->find($id);

        if (! $dispute) {
            return $this->notFound('Payment dispute not found');
        }

        if (! $dispute->canBeResolved()) {
            return $this->error('This dispute cannot be resolved', 400);
        }

        // Validate refund amount for partial refunds
        if ($request->resolution === 'partial_refund') {
            if (! $request->has('refund_amount') || $request->refund_amount <= 0) {
                return $this->error('Refund amount is required for partial refunds', 400);
            }

            if ($request->refund_amount > $dispute->paymentMatch->amount) {
                return $this->error('Refund amount cannot exceed match amount', 400);
            }
        }

        try {
            DB::beginTransaction();

            $match = $dispute->paymentMatch;
            $withdraw = $match->withdraw;

            // Resolve the dispute directly
            $dispute->status = 'resolved';
            $dispute->resolution = $request->resolution;
            $dispute->resolve_user_id = auth()->user()->id;
            $dispute->resolution_notes = $request->resolution_notes;
            $dispute->refund_amount = $request->refund_amount;
            $dispute->resolved_at = now();
            $dispute->save();

            // Update payment match status based on resolution
            switch ($request->resolution) {
                case 'confirmed':
                    $match->status = 'confirmed';
                    break;
                case 'cancelled':
                    $match->status = 'pending';
                    // Adjust withdraw amount matched
                    $withdraw->amount_matched = max(0, $withdraw->amount_matched - $match->amount);
                    $withdraw->save();
                    break;
                case 'partial_refund':
                    $match->status = 'confirmed';
                    // Adjust withdraw amount matched for partial refund
                    $refundAmount = $request->refund_amount;
                    $withdraw->amount_matched = max(0, $withdraw->amount_matched - $refundAmount);
                    $withdraw->save();
                    break;
            }

            $match->save();

            // Check if withdraw should be updated to completed status
            if ($request->resolution === 'confirmed') {
                $allConfirmed = $withdraw->paymentMatches()
                    ->where('status', '!=', 'confirmed')
                    ->count() === 0;

                if ($allConfirmed && $withdraw->isFullyMatched()) {
                    $withdraw->status = 'completed';
                    $withdraw->save();

                    // Update user stats if completed
                    $userStat = $withdraw->user->stats;
                    if ($userStat) {
                        if ($withdraw->fund->currency === 'fiat') {
                            $userStat->incrementTotalFiatWithdrawn($withdraw->amount_matched);
                            $userStat->decrementPendingFiatWithdrawal($withdraw->total_withdrawable_amount);
                        } else {
                            $userStat->incrementTotalCryptoWithdrawn($withdraw->amount_matched);
                            $userStat->decrementPendingCryptoWithdrawal($withdraw->total_withdrawable_amount);
                        }
                        $userStat->save();
                    }
                }
            }

            // Log the resolution
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_DISPUTE_RESOLVED,
                null,
                [
                    'dispute_id' => $dispute->id,
                    'resolution' => $request->resolution,
                    'refund_amount' => $request->refund_amount,
                    'match_id' => $match->id,
                    'resolution_notes' => $request->resolution_notes,
                ]
            );

            DB::commit();

            return $this->success(
                $dispute->load(['paymentMatch', 'disputeUser', 'resolveUser']),
                'Payment dispute resolved successfully'
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to resolve payment dispute: ' . $e->getMessage());
        }
    }

    /**
     * Reject a payment dispute (admin only).
     */
    public function reject(Request $request, string $id): JsonResponse
    {
        if (! auth()->user()->isAdmin()) {
            return $this->forbidden('Only admins can reject disputes');
        }

        $validator = Validator::make($request->all(), [
            'rejection_notes' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $dispute = PaymentDispute::with(['paymentMatch.withdraw', 'paymentMatch.fund'])->find($id);

        if (! $dispute) {
            return $this->notFound('Payment dispute not found');
        }

        if (! $dispute->canBeResolved()) {
            return $this->error('This dispute cannot be rejected', 400);
        }

        try {
            DB::beginTransaction();

            $match = $dispute->paymentMatch;

            // Reject the dispute directly
            $dispute->status = 'rejected';
            $dispute->resolve_user_id = auth()->user()->id;
            $dispute->resolution_notes = $request->rejection_notes;
            $dispute->resolved_at = now();
            $dispute->save();

            // Return payment match to previous status (usually 'paid')
            $match->status = 'paid';
            $match->save();

            // Log the rejection
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_DISPUTE_REJECTED,
                null,
                [
                    'dispute_id' => $dispute->id,
                    'match_id' => $match->id,
                    'rejection_notes' => $request->rejection_notes,
                ]
            );

            DB::commit();

            return $this->success(
                $dispute->load(['paymentMatch', 'disputeUser', 'resolveUser']),
                'Payment dispute rejected successfully'
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to reject payment dispute: ' . $e->getMessage());
        }
    }

    /**
     * Get dispute statistics (admin only).
     */
    public function statistics(Request $request): JsonResponse
    {
        if (! auth()->user()->isAdmin()) {
            return $this->forbidden('Only admins can view dispute statistics');
        }

        // Get date range for statistics
        $startDate = $request->start_date ? date($request->start_date) : now()->subDays(30);
        $endDate = $request->end_date ? date($request->end_date) : now();

        $statistics = [
            'counts' => [
                'total_count' => PaymentDispute::withinDateRange($startDate, $endDate)->count(),
                'statuses' => [
                    'under_review' => PaymentDispute::underReview()->withinDateRange($startDate, $endDate)->count(),
                    'resolved' => PaymentDispute::resolved()->withinDateRange($startDate, $endDate)->count(),
                    'rejected' => PaymentDispute::rejected()->withinDateRange($startDate, $endDate)->count(),
                ],
                'resolutions' => [
                    'confirmed' => PaymentDispute::resolved()->where('resolution', 'confirmed')->withinDateRange($startDate, $endDate)->count(),
                    'cancelled' => PaymentDispute::resolved()->where('resolution', 'cancelled')->withinDateRange($startDate, $endDate)->count(),
                    'partial_refund' => PaymentDispute::resolved()->where('resolution', 'partial_refund')->withinDateRange($startDate, $endDate)->count(),
                ],
                'total_refund_amount' => PaymentDispute::resolved()
                    ->where('resolution', 'partial_refund')
                    ->withinDateRange($startDate, $endDate)
                    ->sum('refund_amount'),
            ],
        ];

        return $this->success($statistics, 'Dispute statistics retrieved successfully');
    }
}
