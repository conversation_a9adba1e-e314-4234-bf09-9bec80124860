// Common payment match types used across both funds and withdraws
import type { Fund } from './fund';
import type { PaymentMethod } from './paymentMethod';
import type { User } from './auth';
import type { PaymentDispute } from './dispute';

export interface PaymentMatch {
  id: string;
  fund_id: string | null;
  withdraw_id: string;
  fund_user_id?: string;
  withdraw_user_id?: string;
  amount: number;
  currency?: string;
  status: 'pending' | 'paid' | 'confirmed' | 'completed' | 'disputed' | 'cancelled';
  transaction_hash?: string;
  payment_proof_image?: string;
  created_at: string;
  updated_at: string;
  // Confirmation fields
  is_payment_sent_confirmed?: boolean;
  payment_sent_confirmed_at?: string;
  is_payment_received_confirmed?: boolean;
  payment_received_confirmed_at?: string;
  // Admin withdraw flag
  is_admin_withdraw?: boolean;

  // Relationships that may be included
  fund?: Fund;
  payment_method?: PaymentMethod;
  fund_user?: User;
  withdraw_user?: User;
  dispute?: PaymentDispute;
}
