import { useState, useEffect } from 'react';
import {
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
  User,
  Plus,
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Badge } from '../ui';
import { disputeService } from '../../services';
import { formatCurrencyAmount } from '../../utils/format';
import { useAuth } from '@/contexts';
import toast from 'react-hot-toast';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { PaymentDispute, CreateDisputeRequest } from '../../types/dispute';

interface DisputeModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentMatch: PaymentMatch;
  onDisputeCreated?: (dispute: PaymentDispute) => void;
}

export function DisputeModal({
  isOpen,
  onClose,
  paymentMatch,
  onDisputeCreated,
}: DisputeModalProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDisputes, setIsLoadingDisputes] = useState(false);
  const [reason, setReason] = useState('');
  const [viewMode, setViewMode] = useState<'list' | 'create'>('list');
  const [disputes, setDisputes] = useState<PaymentDispute[]>([]);

  // Load disputes when modal opens
  useEffect(() => {
    if (isOpen) {
      loadDisputes();
    }
  }, [isOpen, paymentMatch.id]);

  const loadDisputes = async () => {
    setIsLoadingDisputes(true);
    try {
      const response = await disputeService.getDisputesByMatch(paymentMatch.id);
      if (response.status === 'success' && response.data) {
        setDisputes(response.data);
      }
    } catch (error) {
      console.error('Failed to load disputes:', error);
    } finally {
      setIsLoadingDisputes(false);
    }
  };

  // Check if current user can create a dispute
  const canUserCreateDispute = () => {
    if (!user) return false;

    // Check if user is involved in this match
    const isUserInvolved = paymentMatch.fund_user_id === user.id || paymentMatch.withdraw_user_id === user.id;
    if (!isUserInvolved) return false;

    // Check if match is in disputable state
    if (paymentMatch.status !== 'paid') return false;

    // Check if user already has an unresolved dispute
    const userHasActiveDispute = disputes.some(
      dispute => dispute.dispute_user_id === user.id && dispute.status === 'under_review'
    );

    return !userHasActiveDispute;
  };

  const handleCreateDispute = async () => {
    if (!reason.trim()) {
      toast.error('Please provide a reason for the dispute');
      return;
    }
    setIsLoading(true);
    try {
      const data: CreateDisputeRequest = { reason: reason.trim() };
      const response = await disputeService.createDispute(
        paymentMatch.id,
        data
      );

      if (response.status === 'success' && response.data) {
        toast.success(
          'Dispute created successfully. An admin will review your case.'
        );
        onDisputeCreated?.(response.data.dispute);
        setReason('');
        setViewMode('list');
        loadDisputes(); // Reload disputes to show the new one
      } else {
        toast.error(response.message || 'Failed to create dispute');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to create dispute');
    } finally {
      setIsLoading(false);
    }
  };

  const getDisputeStatusBadge = (status: string) => {
    const variants = {
      under_review: 'bg-warning/20 text-warning border-warning/30',
      resolved: 'bg-success/20 text-success border-success/30',
      rejected: 'bg-destructive/20 text-destructive border-destructive/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  const getDisputeStatusIcon = (status: string) => {
    switch (status) {
      case 'under_review':
        return <Clock className='h-3 w-3' />;
      case 'resolved':
        return <CheckCircle className='h-3 w-3' />;
      case 'rejected':
        return <XCircle className='h-3 w-3' />;
      default:
        return <AlertCircle className='h-3 w-3' />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'under_review':
        return 'Under Review';
      case 'resolved':
        return 'Resolved';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  };

  const getResolutionText = (resolution?: string) => {
    switch (resolution) {
      case 'confirmed':
        return 'Payment Confirmed';
      case 'cancelled':
        return 'Payment Cancelled';
      case 'partial_refund':
        return 'Partial Refund Processed';
      default:
        return 'No resolution';
    }
  };

  const getUserRole = (disputeUserId: string) => {
    if (disputeUserId === paymentMatch.fund_user_id) {
      return 'Funder';
    } else if (disputeUserId === paymentMatch.withdraw_user_id) {
      return 'Withdrawer';
    }
    return 'Unknown';
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={disputes.length > 0 ? 'Payment Disputes' : 'Create Payment Dispute'}
      size='lg'
    >
      <div className='space-y-6'>
        {/* Payment Match Info */}
        <div className='bg-background-secondary rounded-lg p-4 border border-border'>
          <h3 className='text-sm font-medium text-foreground mb-3'>
            Payment Match Information
          </h3>
          <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='text-foreground-secondary'>Match ID:</span>
              <p className='font-mono text-foreground'>
                {paymentMatch.id.slice(0, 8)}...
              </p>
            </div>
            <div>
              <span className='text-foreground-secondary'>Amount:</span>
              <p className='font-medium text-foreground'>
                {formatCurrencyAmount(
                  paymentMatch.amount,
                  paymentMatch.fund?.currency || 'fiat'
                )}
              </p>
            </div>
            <div>
              <span className='text-foreground-secondary'>Status:</span>
              <p className='font-medium capitalize text-foreground'>
                {paymentMatch.status}
              </p>
            </div>
            <div>
              <span className='text-foreground-secondary'>Created:</span>
              <p className='text-foreground'>
                {new Date(paymentMatch.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* Disputes List */}
        {viewMode === 'list' && (
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-medium text-foreground'>
                Disputes ({disputes.length})
              </h3>
              {canUserCreateDispute() && (
                <Button
                  onClick={() => setViewMode('create')}
                  variant='outline'
                  size='sm'
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4' />
                  Create Dispute
                </Button>
              )}
            </div>

            {isLoadingDisputes ? (
              <div className='text-center py-8'>
                <div className='text-foreground-secondary'>Loading disputes...</div>
              </div>
            ) : disputes.length === 0 ? (
              <div className='text-center py-8'>
                <div className='text-foreground-secondary mb-4'>
                  No disputes found for this payment match.
                </div>
                {canUserCreateDispute() && (
                  <Button
                    onClick={() => setViewMode('create')}
                    variant='primary'
                    size='sm'
                  >
                    Create First Dispute
                  </Button>
                )}
              </div>
            ) : (
              <div className='space-y-3'>
                {disputes.map((dispute) => (
                  <div
                    key={dispute.id}
                    className='bg-background-secondary rounded-lg p-4 border border-border'
                  >
                    <div className='flex items-start justify-between mb-3'>
                      <div className='flex items-center gap-2'>
                        <User className='h-4 w-4 text-foreground-secondary' />
                        <span className='text-sm font-medium text-foreground'>
                          {dispute.dispute_user?.full_name || 'Unknown User'}
                        </span>
                        <Badge variant='outline' className='text-xs'>
                          {getUserRole(dispute.dispute_user_id)}
                        </Badge>
                        {dispute.dispute_user_id === user?.id && (
                          <Badge variant='default' className='text-xs'>
                            You
                          </Badge>
                        )}
                      </div>
                      <div className='flex items-center gap-2'>
                        {getDisputeStatusIcon(dispute.status)}
                        <Badge className={getDisputeStatusBadge(dispute.status)}>
                          {getStatusText(dispute.status)}
                        </Badge>
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <div>
                        <span className='text-xs text-foreground-secondary'>Reason:</span>
                        <p className='text-sm text-foreground mt-1'>{dispute.reason}</p>
                      </div>

                      <div className='grid grid-cols-2 gap-4 text-xs'>
                        <div>
                          <span className='text-foreground-secondary'>Disputed:</span>
                          <p className='text-foreground'>
                            {new Date(dispute.disputed_at).toLocaleDateString()}
                          </p>
                        </div>
                        {dispute.resolved_at && (
                          <div>
                            <span className='text-foreground-secondary'>Resolved:</span>
                            <p className='text-foreground'>
                              {new Date(dispute.resolved_at).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                      </div>

                      {dispute.status === 'resolved' && dispute.resolution && (
                        <div className='mt-3 p-3 bg-success/10 border border-success/20 rounded-md'>
                          <div className='flex items-center gap-2 mb-2'>
                            <CheckCircle className='h-4 w-4 text-success' />
                            <span className='text-sm font-medium text-success'>
                              {getResolutionText(dispute.resolution)}
                            </span>
                          </div>
                          {dispute.resolution_notes && (
                            <p className='text-xs text-foreground-secondary'>
                              {dispute.resolution_notes}
                            </p>
                          )}
                          {dispute.refund_amount && (
                            <p className='text-xs text-foreground mt-1'>
                              Refund Amount: {formatCurrencyAmount(
                                dispute.refund_amount,
                                paymentMatch.fund?.currency || 'fiat'
                              )}
                            </p>
                          )}
                        </div>
                      )}

                      {dispute.status === 'rejected' && (
                        <div className='mt-3 p-3 bg-destructive/10 border border-destructive/20 rounded-md'>
                          <div className='flex items-center gap-2 mb-2'>
                            <XCircle className='h-4 w-4 text-destructive' />
                            <span className='text-sm font-medium text-destructive'>
                              Dispute Rejected
                            </span>
                          </div>
                          {dispute.resolution_notes && (
                            <p className='text-xs text-foreground-secondary'>
                              {dispute.resolution_notes}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {!canUserCreateDispute() && disputes.length > 0 && (
              <div className='bg-warning/10 border border-warning/20 rounded-md p-4'>
                <div className='flex items-center'>
                  <AlertCircle className='h-5 w-5 text-warning mr-3' />
                  <div className='text-sm text-foreground'>
                    <p className='font-medium'>Cannot Create New Dispute</p>
                    <p className='mt-1 text-foreground-secondary'>
                      {paymentMatch.status !== 'paid'
                        ? 'You can only dispute payments that have been marked as "paid"'
                        : 'You already have an unresolved dispute for this payment match'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        {/* Create Dispute Form */}
        {viewMode === 'create' && (
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-medium text-foreground'>Create New Dispute</h3>
              <Button
                onClick={() => setViewMode('list')}
                variant='outline'
                size='sm'
              >
                Back to List
              </Button>
            </div>

            <div className='bg-warning/10 border border-warning/20 rounded-md p-4'>
              <div className='flex items-start'>
                <AlertCircle className='h-5 w-5 text-warning mt-0.5 mr-3' />
                <div className='text-sm text-foreground'>
                  <p className='font-medium'>Important Information</p>
                  <ul className='mt-2 list-disc list-inside space-y-1 text-foreground-secondary'>
                    <li>
                      Only create a dispute if you believe there's a legitimate
                      issue with the payment
                    </li>
                    <li>
                      An admin will review your dispute and make a decision
                    </li>
                    <li>
                      You can only dispute matches that have been marked as "paid"
                    </li>
                    <li>False disputes may result in account restrictions</li>
                  </ul>
                </div>
              </div>
            </div>

            {!canUserCreateDispute() && (
              <div className='bg-destructive/10 border border-destructive/20 rounded-md p-4'>
                <div className='flex items-center'>
                  <XCircle className='h-5 w-5 text-destructive mr-3' />
                  <div className='text-sm text-foreground'>
                    <p className='font-medium'>Cannot Create Dispute</p>
                    <p className='mt-1 text-foreground-secondary'>
                      {paymentMatch.status !== 'paid'
                        ? 'You can only dispute payments that have been marked as "paid"'
                        : 'You already have an unresolved dispute for this payment match'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {canUserCreateDispute() && (
              <div>
                <label
                  htmlFor='reason'
                  className='block text-sm font-medium text-foreground mb-2'
                >
                  Reason for Dispute <span className='text-destructive'>*</span>
                </label>
                <textarea
                  id='reason'
                  value={reason}
                  onChange={e => setReason(e.target.value)}
                  className='w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none'
                  rows={4}
                  placeholder='Please describe the issue with this payment match in detail...'
                  maxLength={1000}
                  disabled={isLoading}
                />
                <div className='mt-1 text-xs text-foreground-secondary text-right'>
                  {reason.length}/1000 characters
                </div>
              </div>
            )}
          </div>
        )}
        {/* Footer */}
        <div className='flex justify-end gap-3 pt-4 border-t border-border'>
          <Button variant='outline' onClick={onClose} disabled={isLoading}>
            Close
          </Button>

          {viewMode === 'create' && canUserCreateDispute() && (
            <Button
              onClick={handleCreateDispute}
              disabled={isLoading || !reason.trim()}
              variant='destructive'
            >
              {isLoading ? 'Creating...' : 'Create Dispute'}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
