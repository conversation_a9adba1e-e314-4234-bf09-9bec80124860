import React, { useState } from 'react';
import {
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
  User,
  FileText,
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Badge } from '../ui';
import { disputeService } from '../../services';
import { formatCurrencyAmount } from '../../utils/format';
import toast from 'react-hot-toast';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { PaymentDispute, CreateDisputeRequest } from '../../types/dispute';

interface DisputeModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentMatch: PaymentMatch;
  onDisputeCreated?: (dispute: PaymentDispute) => void;
}

export function DisputeModal({
  isOpen,
  onClose,
  paymentMatch,
  onDisputeCreated,
}: DisputeModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [reason, setReason] = useState('');
  const [viewMode, setViewMode] = useState<'create' | 'view'>('create');

  // Check if there's already a dispute
  const existingDispute = paymentMatch.dispute;
  const hasDispute = !!existingDispute;

  console.log('DisputeModal paymentMatch:', paymentMatch);

  // Set view mode based on whether dispute exists
  React.useEffect(() => {
    setViewMode(hasDispute ? 'view' : 'create');
  }, [hasDispute]);

  const handleCreateDispute = async () => {
    if (!reason.trim()) {
      toast.error('Please provide a reason for the dispute');
      return;
    }
    setIsLoading(true);
    try {
      const data: CreateDisputeRequest = { reason: reason.trim() };
      const response = await disputeService.createDispute(
        paymentMatch.id,
        data
      );

      if (response.status === 'success' && response.data) {
        toast.success(
          'Dispute created successfully. An admin will review your case.'
        );
        onDisputeCreated?.(response.data.dispute);
        onClose();
        setReason('');
      } else {
        toast.error(response.message || 'Failed to create dispute');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to create dispute');
    } finally {
      setIsLoading(false);
    }
  };

  const getDisputeStatusBadge = (status: string) => {
    const variants = {
      under_review: 'bg-warning/20 text-warning border-warning/30',
      resolved: 'bg-success/20 text-success border-success/30',
      rejected: 'bg-destructive/20 text-destructive border-destructive/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  const getDisputeStatusIcon = (status: string) => {
    switch (status) {
      case 'under_review':
        return <Clock className='h-3 w-3' />;
      case 'resolved':
        return <CheckCircle className='h-3 w-3' />;
      case 'rejected':
        return <XCircle className='h-3 w-3' />;
      default:
        return <AlertCircle className='h-3 w-3' />;
    }
  };

  const getResolutionText = (resolution?: string) => {
    switch (resolution) {
      case 'confirmed':
        return 'Payment Confirmed';
      case 'cancelled':
        return 'Payment Cancelled';
      case 'partial_refund':
        return 'Partial Refund Processed';
      default:
        return 'No resolution';
    }
  };

  const canCreateDispute = () => {
    return paymentMatch.status === 'paid' && !hasDispute;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={hasDispute ? 'Payment Dispute Details' : 'Create Payment Dispute'}
      size='md'
    >
      <div className='space-y-6'>
        {' '}
        {/* Payment Match Info */}
        <div className='bg-background-secondary rounded-lg p-4 border border-border'>
          <h3 className='text-sm font-medium text-foreground mb-3'>
            Payment Match Information
          </h3>
          <div className='grid grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='text-foreground-secondary'>Match ID:</span>
              <p className='font-mono text-foreground'>
                {paymentMatch.id.slice(0, 8)}...
              </p>
            </div>
            <div>
              <span className='text-foreground-secondary'>Amount:</span>
              <p className='font-medium text-foreground'>
                {formatCurrencyAmount(
                  paymentMatch.amount,
                  paymentMatch.fund?.currency || 'fiat'
                )}
              </p>
            </div>
            <div>
              <span className='text-foreground-secondary'>Status:</span>
              <p className='font-medium capitalize text-foreground'>
                {paymentMatch.status}
              </p>
            </div>
            <div>
              <span className='text-foreground-secondary'>Created:</span>
              <p className='text-foreground'>
                {new Date(paymentMatch.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
        {/* Create Dispute Form */}
        {viewMode === 'create' && (
          <div className='space-y-4'>
            {' '}
            <div className='bg-warning/10 border border-warning/20 rounded-md p-4'>
              <div className='flex items-start'>
                <AlertCircle className='h-5 w-5 text-warning mt-0.5 mr-3' />
                <div className='text-sm text-foreground'>
                  <p className='font-medium'>Important Information</p>
                  <ul className='mt-2 list-disc list-inside space-y-1 text-foreground-secondary'>
                    <li>
                      Only create a dispute if you believe there's a legitimate
                      issue with the payment
                    </li>
                    <li>
                      An admin will review your dispute and make a decision
                    </li>
                    <li>
                      You can only dispute matches that have been marked as
                      "paid"
                    </li>
                    <li>False disputes may result in account restrictions</li>
                  </ul>
                </div>
              </div>
            </div>{' '}
            {!canCreateDispute() && (
              <div className='bg-destructive/10 border border-destructive/20 rounded-md p-4'>
                <div className='flex items-center'>
                  <XCircle className='h-5 w-5 text-destructive mr-3' />
                  <div className='text-sm text-foreground'>
                    <p className='font-medium'>Cannot Create Dispute</p>
                    <p className='mt-1 text-foreground-secondary'>
                      {hasDispute
                        ? 'A dispute already exists for this payment match'
                        : 'You can only dispute payments that have been marked as "paid"'}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {canCreateDispute() && (
              <>
                {' '}
                <div>
                  <label
                    htmlFor='reason'
                    className='block text-sm font-medium text-foreground mb-2'
                  >
                    Reason for Dispute{' '}
                    <span className='text-destructive'>*</span>
                  </label>
                  <textarea
                    id='reason'
                    value={reason}
                    onChange={e => setReason(e.target.value)}
                    className='w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none'
                    rows={4}
                    placeholder='Please describe the issue with this payment match in detail...'
                    maxLength={1000}
                    disabled={isLoading}
                  />
                  <div className='mt-1 text-xs text-foreground-secondary text-right'>
                    {reason.length}/1000 characters
                  </div>
                </div>
              </>
            )}
          </div>
        )}
        {/* View Existing Dispute */}
        {viewMode === 'view' && existingDispute && (
          <div className='space-y-4'>
            {' '}
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-medium text-foreground'>
                Dispute Information
              </h3>
              <Badge
                variant='outline'
                className={`${getDisputeStatusBadge(existingDispute.status)} flex items-center gap-1 px-2 py-1 text-xs font-medium border`}
              >
                {getDisputeStatusIcon(existingDispute.status)}
                {existingDispute.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
              <div>
                <span className='text-foreground-secondary'>Dispute ID:</span>
                <p className='font-mono text-foreground'>
                  {existingDispute.id.slice(0, 8)}...
                </p>
              </div>
              <div>
                <span className='text-foreground-secondary'>Created:</span>
                <p className='text-foreground'>
                  {new Date(existingDispute.disputed_at).toLocaleString()}
                </p>
              </div>
              {existingDispute.resolved_at && (
                <div>
                  <span className='text-foreground-secondary'>Resolved:</span>
                  <p className='text-foreground'>
                    {new Date(existingDispute.resolved_at).toLocaleString()}
                  </p>
                </div>
              )}
              {existingDispute.resolution && (
                <div>
                  <span className='text-foreground-secondary'>Resolution:</span>
                  <p className='text-foreground'>
                    {getResolutionText(existingDispute.resolution)}
                  </p>
                </div>
              )}
            </div>{' '}
            <div>
              <label className='block text-sm font-medium text-foreground mb-2'>
                <FileText className='inline h-4 w-4 mr-1' />
                Your Reason
              </label>
              <div className='bg-background-secondary border border-border rounded-md p-3'>
                <p className='text-sm text-foreground whitespace-pre-wrap'>
                  {existingDispute.reason}
                </p>
              </div>
            </div>
            {existingDispute.resolution_notes && (
              <div>
                <label className='block text-sm font-medium text-foreground mb-2'>
                  <User className='inline h-4 w-4 mr-1' />
                  Admin Response
                </label>
                <div className='bg-primary/10 border border-primary/20 rounded-md p-3'>
                  <p className='text-sm text-foreground whitespace-pre-wrap'>
                    {existingDispute.resolution_notes}
                  </p>
                </div>
              </div>
            )}
            {existingDispute.refund_amount && (
              <div>
                <label className='block text-sm font-medium text-foreground mb-2'>
                  Refund Amount
                </label>
                <div className='bg-success/10 border border-success/20 rounded-md p-3'>
                  <p className='text-sm text-success font-medium'>
                    {formatCurrencyAmount(
                      existingDispute.refund_amount,
                      paymentMatch.fund?.currency || 'fiat'
                    )}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}{' '}
        {/* Footer */}
        <div className='flex justify-end gap-3 pt-4 border-t border-border'>
          <Button variant='outline' onClick={onClose} disabled={isLoading}>
            {hasDispute ? 'Close' : 'Cancel'}
          </Button>

          {viewMode === 'create' && canCreateDispute() && (
            <Button
              onClick={handleCreateDispute}
              disabled={isLoading || !reason.trim()}
              variant='destructive'
            >
              {isLoading ? 'Creating...' : 'Create Dispute'}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
