<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_matches', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('fund_id');
            $table->uuid('withdraw_id');
            $table->uuid('fund_user_id');
            $table->uuid('withdraw_user_id');
            $table->decimal('amount', 20, 8);
            $table->uuid('payment_method_id');
            $table->string('payment_proof_image')->nullable();
            $table->string('transaction_hash')->nullable();
            $table->enum('initiator', ['auto', 'manual'])->default('auto');
            $table->enum('status', ['pending', 'paid', 'confirmed', 'disputed'])->default('pending');
            $table->boolean('is_payment_sent_confirmed')->default(false);
            $table->timestamp('payment_sent_confirmed_at')->nullable();
            $table->boolean('is_payment_received_confirmed')->default(false);
            $table->timestamp('payment_received_confirmed_at')->nullable();
            $table->boolean('is_admin_withdraw')->default(false);
            $table->timestamps();

            $table->foreign('fund_id')->references('id')->on('funds')->onDelete('restrict');
            $table->foreign('withdraw_id')->references('id')->on('withdraws')->onDelete('restrict');
            $table->foreign('fund_user_id')->references('id')->on('users')->onDelete('restrict');
            $table->foreign('withdraw_user_id')->references('id')->on('users')->onDelete('restrict');
            $table->foreign('payment_method_id')->references('id')->on('payment_methods')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_matches');
    }
};
